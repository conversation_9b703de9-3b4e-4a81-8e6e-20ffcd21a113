﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.Logging;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.OrganizationMappings;
using MyAdaptiveCloud.Data.Repositories;
using MyAdaptiveCloud.Services.Apis.CloudStack;
using MyAdaptiveCloud.Services.Apis.CloudStack.Model;
using MyAdaptiveCloud.Services.Apis.CloudStack.Requests;
using MyAdaptiveCloud.Services.Apis.ConnectWise;
using MyAdaptiveCloud.Services.Apis.ConnectWise.Model;
using MyAdaptiveCloud.Services.Apis.PowerDNS;
using MyAdaptiveCloud.Services.DTOs.Configuration;
using MyAdaptiveCloud.Services.DTOs.Registration;
using MyAdaptiveCloud.Services.Exceptions;
using MyAdaptiveCloud.Services.Requests.Organizations;
using MyAdaptiveCloud.Services.Requests.Registration;
using System.Data;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Text.Json;
using static MyAdaptiveCloud.Services.Requests.Registration.ProvisioningRequest;
using Contact = MyAdaptiveCloud.Services.Apis.ConnectWise.Model.Contact;
using Dto = MyAdaptiveCloud.Services.DTOs.AdaptiveCloud;
using OrganizationData = MyAdaptiveCloud.Data.MyAdaptiveCloud;
using Person = MyAdaptiveCloud.Data.MyAdaptiveCloud.Person;

namespace MyAdaptiveCloud.Services.Services
{
    public class RegistrationService : IRegistrationService
    {
        private readonly IConfigurationService _configurationService;
        private readonly ICloudStackApi _cloudStackApi;
        private readonly IConnectWiseApi _connectWiseApi;
        private readonly IConnectWiseService _connectWiseService;
        private readonly IAdaptiveCloudService _adaptiveCloudService;
        private readonly IPersonService _personService;
        private readonly IOrganizationService _organizationService;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IPowerDNSApi _powerDNSApi;
        private readonly MyAdaptiveCloudContext _dbContext;
        private readonly ILogger<IRegistrationService> _logger;
        private readonly IWhiteLabelService _whiteLabelService;
        private readonly IUserRoleRepository _userRoleRepository;
        private readonly IOrganizationMappingRepository _organizationMappingRepository;
        private readonly IEmailSenderService _emailSenderService;
        private readonly IPersonRepository _personRepository;
        private readonly IUserService _userService;
        private readonly IInvitationCodeRepository _invitationCodeRepository;
        private readonly IMapper _mapper;
        private readonly IUserRepository _userRepository;

        public RegistrationService(
            IConfigurationService configurationService,
            ICloudStackApi cloudStackApi,
            IOrganizationRepository organizationDataService,
            IConnectWiseApi connectWiseApi,
            IConnectWiseService connectWiseService,
            IPersonService personService,
            ILogger<IRegistrationService> logger,
            IOrganizationService organizationService,
            IAdaptiveCloudService adaptiveCloudService,
            IPowerDNSApi powerDNS,
            IWhiteLabelService whiteLabelService,
            IUserRoleRepository userRoleRepository,
            MyAdaptiveCloudContext context,
            IEmailSenderService emailSenderService,
            IOrganizationMappingRepository organizationMappingRepository,
            IMapper mapper,
            IPersonRepository personRepository,
            IUserService userService,
            IInvitationCodeRepository invitationCodeRepository,
            IUserRepository userRepository
        )
        {
            _configurationService = configurationService;
            _cloudStackApi = cloudStackApi;
            _connectWiseApi = connectWiseApi;
            _connectWiseService = connectWiseService;
            _adaptiveCloudService = adaptiveCloudService;
            _personService = personService;
            _dbContext = context;
            _organizationService = organizationService;
            _organizationRepository = organizationDataService;
            _powerDNSApi = powerDNS;
            _logger = logger;
            _whiteLabelService = whiteLabelService;
            _userRoleRepository = userRoleRepository;
            _emailSenderService = emailSenderService;
            _organizationMappingRepository = organizationMappingRepository;
            _mapper = mapper;
            _personRepository = personRepository;
            _userService = userService;
            _invitationCodeRepository = invitationCodeRepository;
            _userRepository = userRepository;
        }

        public async Task<bool> IsDomainAvailable(string domainName, string adaptiveCloudDomain)
        {
            // If we get zero hits on this name, then the name must be available in Cloud Infrastructure
            var domains = await _cloudStackApi.GetDomains(new DomainRequest() { Name = domainName, Level = null });

            // If we get zero hits on this, then the name must be available in DNS
            bool dnsExists = false;
            if (adaptiveCloudDomain != null)
            {
                try
                {
                    var entry = Dns.GetHostEntry(domainName + "." + adaptiveCloudDomain);
                    dnsExists = true;
                }
                catch (System.Net.Sockets.SocketException)
                {
                    dnsExists = false;
                }
            }

            return !dnsExists && (domains == null || domains.Count == 0);
        }

        public async Task<string> SetupCloudInfrastructure(int userId, int organizationId, CreateDomainRequest createDomainRequest,
            CheckCloudInfrastructureResponse validationResponse, string userTimeZone,
            ProvisioningRegistrationFlow provisioningFlow = ProvisioningRegistrationFlow.None)
        {
            var organization = await _organizationRepository.GetActiveOrganizationById(organizationId, true);
            var isPartner = validationResponse.IsPartner || organization.IsPartner;

            var parentOrganization = await _organizationService.GetRootOrganization(organizationId, true);

            if (validationResponse.ValidationState != CloudInfrastructureValidationState.Ok)
            {
                // Throw an exception? Should never get here!!!
                throw new Exception("Cloud Infrastructure State != Ok, a problem has occurred");
            }

            Domain domainCreated = null;
            Domain domain = null;
            if (validationResponse.NeedsCloudInfrastructure)
            {
                if (createDomainRequest != null && isPartner)
                {
                    (var domainName, var adaptiveCloudDomain) = await GetDomainInfoFromHostName(createDomainRequest.HostName);

                    if (!await IsDomainAvailable(domainName, adaptiveCloudDomain))
                    {
                        throw new BadRequestException("Domain is not available");
                    }

                    domain = await _cloudStackApi.CreateDomain(new Domain() { Name = domainName, NetworkDomain = $"{domainName}.internal" });
                    domainCreated = domain;
                    await _adaptiveCloudService.UpdateAllResourceLimits(domain.Id, accountName: null);

                    // Associate the hostname with the WhiteLabel for this MYAC Org so that the launch link works for Cloud Infrastructure
                    var whiteLabel = new EditWhiteLabelRequest
                    {
                        PortalName = validationResponse.Company != null ? validationResponse.Company.Name : organization.Name,
                        // TODO Do we want to hook up a white label for MYAC for them also?
                        // DomainName = "DNS entry for MYAC";
                        // TODO Should we be doing this if the domain is not one of ours, as they wouldn't have yet added their own CNAME yet?
                        AdaptiveCloudUrl = createDomainRequest.HostName,
                    };
                    await _whiteLabelService.CreateWhiteLabel(userId, organizationId, whiteLabel);

                    // Add a DNS entry if the hostname is one of ours!
                    if (adaptiveCloudDomain != null)
                    {
                        var cname = (await _configurationService.GetCloudInfrastructureConfiguration()).CName;
                        await _powerDNSApi.AddCNameRecord(adaptiveCloudDomain, createDomainRequest.HostName, cname);
                    }
                }

                var accountRequest = new CreateAccountRequest()
                {
                    Account = validationResponse.Company != null ? validationResponse.Company.Name : organization.Name,
                    Accounttype = AccountType.User, // always default account type to User
                    Timezone = userTimeZone, ///< TODO This could fail if the timezone given isn't one of the supported ones in CloudStack
                };


                //Domain
                Domain rootDomain = await _cloudStackApi.GetRootDomain();

                // if new org is a partner and domain is not null and domain is not Root domain, then set account type to DomainAdmin
                if (isPartner && domain != null && rootDomain != null && domain.Id != rootDomain.Id)
                    accountRequest.Accounttype = AccountType.DomainAdmin;

                // Leave domainId null in the request for ROOT (Account creation only).
                if (domain != null)
                {
                    accountRequest.Domainid = domain.Id;
                    validationResponse.DomainId = domain.Id;
                }
                else if (parentOrganization.OrganizationId != Constants.RootOrganizationId && parentOrganization.IsPartner)
                {
                    //CI Account should be created under Parent Partner Domain
                    var parentOrganizationMapping = await _adaptiveCloudService.GetMapping(parentOrganization.OrganizationId);
                    accountRequest.Domainid = parentOrganizationMapping.DomainId ?? null;
                    validationResponse.DomainId = parentOrganizationMapping.DomainId ?? null;
                }
                else
                {
                    // TODO Didn't create a domain, only created an account, so set the networkdomain for the account
                    // TODO However, what do we set it to?
                    //accountRequest.Networkdomain = "";
                }

                // Make sure that the accountrequest domainid isn't null, as we use it below to determine how to query for the user
                accountRequest.Domainid ??= rootDomain.Id;


                //User 
                var user = new Person();
                var isTemporalUser = false;
                //Provisioning Flow
                var loggedUser = await _personRepository.GetPersonById(userId, true);

                _logger.LogInformation($"Starting User Provisioning flow for User: {loggedUser} and OrgID: {organizationId}");

                var organizationUsers = await _personService.GetPersonsWithUserRolesInOrganizationByOrganizationId(organizationId);
                var emailList = organizationUsers.Select(a => a.Email);
                if (emailList.Any(e => string.Equals(e, loggedUser.Email, StringComparison.OrdinalIgnoreCase)))
                {
                    //Logged User exist in MYAC organization
                    var loggedUserAccounts = await _cloudStackApi.GetUsersByUsername(loggedUser.Email);
                    if (loggedUserAccounts.Where(a => a.DomainId == accountRequest.Domainid).Any())
                    {
                        //Logged User already has an account in the same domain
                        user = GetTemporaryUser();
                        isTemporalUser = true;
                    }
                    else
                    {
                        user = loggedUser;
                    }
                }
                else
                {
                    //Logged User not exist in MYAC Organization
                    user = GetTemporaryUser();
                    isTemporalUser = true;
                }

                accountRequest.Username = user.Email;
                accountRequest.Firstname = user.FirstName;
                accountRequest.Lastname = user.LastName;
                accountRequest.Email = user.Email;

                var account = await _cloudStackApi.CreateAccount(accountRequest);

                if (account == null && domainCreated != null)
                {
                    await _cloudStackApi.RemoveDomain(domainCreated, true);
                    // Error
                    throw new BadRequestException("Failed to create Cloud Infrastructure Account");
                }

                validationResponse.Account = account.user.First();

                await _adaptiveCloudService.UpdateAllResourceLimits(account.DomainId, account.Name);

                var idp = await _cloudStackApi.GetIdp();
                var newUser = account.user.FirstOrDefault();
                if (newUser == null)
                {
                    throw new BadRequestException("Cannot find new Cloud Infrastructure User");
                }

                //TODO Added until refactor Registration Process
                if (isTemporalUser || provisioningFlow == ProvisioningRegistrationFlow.SubOrg && newUser.Id != Guid.Empty)
                {
                    var deleteUser = await _cloudStackApi.DeleteUser(newUser.Id.ToString());
                    if (deleteUser.StatusCode != System.Net.HttpStatusCode.OK)
                    {
                        throw new BadRequestException("Issue cannot delete new TempUser");
                    }
                }

                if (!isTemporalUser)
                {
                    await _cloudStackApi.AuthorizeSamlSso(newUser.Id, authorized: true, idp);
                }

                validationResponse.AccountId = account.Id;
            }

            await MapCompanyToCloudInfrastructure(userId, organizationId, validationResponse);

            // Add OrganizationMapping
            var ciMap = await _adaptiveCloudService.GetMapping(organizationId);
            if (ciMap == null)
            {
                ciMap = new Dto.ACOrganizationMappingModel
                {
                    AccountId = validationResponse.AccountId,
                    AccountName = validationResponse.Account.Account,
                };
                if (domain != null)
                {
                    ciMap.DomainId = validationResponse.DomainId;
                    ciMap.DomainName = validationResponse.Account.Domain;
                }

                await _adaptiveCloudService.AddMapping(organizationId, ciMap);
            }

            var modalResponseTitle =
                validationResponse.AccountAlreadyLinked ? "Cloud Infrastructure Account has been linked" :
                domainCreated != null ? "Cloud Infrastructure Domain has been created" :
                "Cloud Infrastructure Account has been created";

            if (validationResponse.NeedsCloudInfrastructure)
            {
                await SendUserRegistrationEmail(organizationId, userId, ciMap.DomainName, "New Cloud Infrastructure Account",
                    "The following user has created a Cloud Infrastructure account");
                return modalResponseTitle;
            }
            else
            {
                await SendUserRegistrationEmail(organizationId, userId, ciMap.DomainName, "Cloud Infrastructure Account",
                    "The following user has associated a Cloud Infrastructure account");
                return modalResponseTitle;
            }
        }

        public async Task<List<RegistrationCompanyDTO>> GetCompaniesByEmail(int userId)
        {
            // Skip ConnectWise integration - return empty list to force new company creation
            // TODO: Re-enable ConnectWise integration when properly configured
            await Task.CompletedTask; // Keep async signature
            return new List<RegistrationCompanyDTO>();
        }

        public async Task<RegistrationResponse> RegisterUserWithNewCompany(CreateCompanyAndContactRequest request, int userId)
        {
            _logger.LogInformation($"Starting register new company process for User Id {userId} - ConnectWise integration skipped");

            var user = await _personRepository.GetPersonById(userId, true);
            var createdDate = DateTime.UtcNow;

            // Skip ConnectWise integration - create organization directly
            var organizationId = await CreateOrganizationWithoutConnectWise(request, userId, createdDate);

            await CreateUserRole(userId, organizationId, createdDate, (int)Roles.Admin, isApproved: true);

            await SendUserRegistrationEmail(organizationId, userId, "", "New MyAdaptiveCloud Registration", "The following user has registered for MyAdaptiveCloud");

            return new RegistrationResponse
            {
                CompanyId = 0, // No ConnectWise company ID
                OrganizationId = organizationId
            };
        }

        private async Task<int> CreateOrganizationWithoutConnectWise(CreateCompanyAndContactRequest request, int userId, DateTime createdDate)
        {
            _logger.LogInformation($"Creating organization without ConnectWise integration for user {userId}");

            // Create organization directly without ConnectWise mapping
            var organization = new MyAdaptiveCloud.Data.MyAdaptiveCloud.Organization
            {
                Name = request.Name,
                IsActive = true,
                IsPartner = request.IsPartner,
                CreatedBy = userId,
                CreatedDate = createdDate,
                AllowSubOrg = false,
                AllowWhiteLabel = false
            };

            _dbContext.Organization.Add(organization);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Created organization {organization.OrganizationId} with name '{organization.Name}'");

            return organization.OrganizationId;
        }

        public async Task<ConnectWiseOrganizationMapping> GetOrganizationMapping(int companyId)
        {
            _logger.LogInformation($"Get Organization Mapping for Company Id {companyId}. Looking into OrganizationMapping table for a row where PrimaryId equals {companyId}");
            var mapping = await _dbContext.ConnectWiseOrganizationMapping
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .FirstOrDefaultAsync(o => o.CompanyId == companyId);
            _logger.LogInformation(mapping != null ? $"Mapping exists. Organization Id: {mapping.OrganizationId}" : "Mapping does not exist");
            return mapping;
        }

        public async Task<UserRole> RegisterUserWithExistingCompany(int companyId, int userId)
        {
            _logger.LogInformation($"Starting register existing company process for User Id {userId} and Company Id {companyId}");

            // The following calls ignore the Organization.IsActive global filter.
            // The organization is made active either when creating it (MapCompanyToNewOrganization) or when approving a user (CreateUserRole)

            var company = await _connectWiseApi.GetCompany(companyId);
            var organizationMapping = await GetOrganizationMapping(companyId);
            var hasUsers = false;

            if (organizationMapping != null)
            {
                var userCount = await _dbContext.User.IgnoreQueryFilters().CountAsync(c => c.OrganizationId == organizationMapping.OrganizationId);
                hasUsers = userCount > 0;
                _logger.LogInformation($"Company {companyId} is mapped to Organization {organizationMapping.OrganizationId} with {userCount} users");
            }
            else
            {
                _logger.LogInformation($"Company {companyId} is not mapped to any Organization");
            }

            var createdDate = DateTime.UtcNow;

            // Company is not mapped to any organization or it is mapped to one that does not have at least one user
            // Create new organization if applicable, amd map the company and create user mapping with admin role
            if (organizationMapping == null || !hasUsers)
            {
                int organizationId;
                if (organizationMapping == null)
                {
                    _logger.LogInformation("Mapping does not exist. Creating company / org mapping");
                    organizationId = await MapCompanyToNewOrganization(userId, company, createdDate);
                    _logger.LogInformation($"Mapping created. Organization {organizationId}");
                }
                else
                {
                    organizationId = organizationMapping.OrganizationId;
                    _logger.LogInformation($"Mapping exists. Organization {organizationId}");
                }

                _logger.LogInformation("Creating mapping for admin role approved");
                var mapping = await CreateUserRole(userId, organizationId, createdDate, (int)Roles.Admin, isApproved: true);

                if (mapping.IsApproved)
                {
                    _logger.LogInformation($"User {userId} is approved. Send Registration email");

                    await SendUserRegistrationEmail(mapping.User.OrganizationId, userId, "", "New MyAdaptiveCloud Registration",
                        "The following user has registered for MyAdaptiveCloud");
                }

                return mapping;
            }
            // Company is mapped to an existing organization:
            // Create user mapping with user role
            else
            {
                _logger.LogInformation("Creating mapping for user role not approved");
                return await CreateUserRole(userId, organizationMapping.OrganizationId, createdDate, (int)Roles.User, isApproved: false);
            }
        }

        public async Task<string> CreateIdentifier(string emailAddress)
        {
            var emailParts = emailAddress.Split('@')[1].Split('.');
            var emailDomain = emailParts[^2];

            var domainPart = emailDomain.Length >= 6 ? emailDomain.Substring(0, 6) : emailDomain;

            var companies = await _connectWiseApi.GetCompaniesByIdentifier(domainPart);

            // Filter companies with identifier starting with the supplied domain name
            // and a length of domain name length + 3
            // and the last three digits can be parsed into a number
            var numberParts = companies?
                .Where(
                    c => c.Identifier.StartsWith(domainPart) &&
                         c.Identifier.Length == domainPart.Length + 3 &&
                         int.TryParse(c.Identifier.Substring(c.Identifier.Length - 3, 3), out int result)
                )
                .Select(c => int.Parse(c.Identifier.Substring(c.Identifier.Length - 3, 3))).ToList();


            if (numberParts == null || !numberParts.Any())
            {
                return domainPart + "001";
            }
            else
            {
                return domainPart + (numberParts.Max() + 1).ToString("D3");
            }
        }

        public async Task<string> SendApprovalEmail(int organizationId, int userId, string host)
        {
            var approverUsers = await _personService.GetPersonsWithManagePendingRolesByOrganization(organizationId);

            if (!approverUsers.Any())
            {
                _logger.LogWarning($"Organization {organizationId} does not have any users with ManagePendingUser permission to approve.");
                return "Registration completed.";
            }

            var approverUserEmails = approverUsers.Select(x => x.Email).ToList();
            var approverUserNames = string.Join(", ", approverUsers.Select(u => u.FullName));
            var newUser = await _personRepository.GetPersonById(userId, true);
            var organization = await _organizationService.GetOrganizationById(organizationId);

            try
            {
                var uri = new Uri("https://" + host + "/users/pendingapproval");
                var message =
                    $"The following user is requesting to be added to {organization.Name}. <a href=\"{uri}\" target=\"_blank\">Click Here</a> to manage your organization's user approvals.<br /><br />Name: {newUser.FullName}<br />Email: {newUser.Email}";
                var smtpConfiguration = await _configurationService.GetEmailSenderWorkerConfiguration();
                await _emailSenderService.EnqueueEmailMessage(approverUserEmails, message.ToString(), "New MyAdaptiveCloud User is Pending Approval", smtpConfiguration.Email);
            }
            catch (SmtpException ex)
            {
                _logger.LogError(ex, $"Error while sending approval email for user {newUser.Email} to {approverUserNames}");
            }

            return $"The following administrators within your organization have been contacted to approve your account activation: {approverUserNames}. " +
                   $"You will receive an email once your account has been activated.";
        }

        public async Task SendUserRegistrationEmail(int organizationId, int userId, string domainName, string subject, string header)
        {
            var user = await _personRepository.GetPersonById(userId, true);
            var organization = await _organizationService.GetOrganizationById(organizationId);

            var smtpConfiguration = await _configurationService.GetEmailSenderWorkerConfiguration();

            try
            {
                var message = new StringBuilder($"{header}:<br/><br/>");
                message.Append($"Email: {user.Email}<br/>");
                message.Append($"First Name: {user.FirstName}<br/>");
                message.Append($"Last Name: {user.LastName}<br/>");
                message.Append($"Organization Name: {organization.Name}");
                if (!string.IsNullOrEmpty(domainName))
                {
                    message.Append($"<br/>Domain Name: {domainName}");
                }

                await _emailSenderService.EnqueueEmailMessage(smtpConfiguration.RegistrationEmail, message.ToString(), subject, smtpConfiguration.Email);
            }
            catch (SmtpException ex)
            {
                _logger.LogError(ex, $"Error while sending {subject} email for user {user.Email} to {smtpConfiguration.RegistrationEmail}");
            }
        }

        public async Task<CheckCloudInfrastructureResponse> CheckCloudInfrastructure(int userId, int organizationId,
            ProvisioningRegistrationFlow provisioningFlow = ProvisioningRegistrationFlow.None)
        {
            _logger.LogInformation($"Check Cloud Infrastructure for User {userId} and Organization {organizationId} and ProvisioningFlow {provisioningFlow}");

            // Fetch this user
            var user = await _personRepository.GetPersonById(userId, true);
            // Fetch organization users
            var organizationUsers = await _personService.GetOrganizationUsersForProvisioning(organizationId, "");
            var isUserInCurrentOrganization = organizationUsers.Where(a => a.Email.ToLower().Trim() == user.Email.ToLower().Trim()).Any();

            var response = new CheckCloudInfrastructureResponse();
            // Fetch this org Billable parents
            var hasBillableOrgs = await _organizationService.GetHasBillableOrgs(organizationId);

            // Fetch their attached CW Company
            var company = await _connectWiseService.GetAssociatedCompany(organizationId);
            if (company == null && !hasBillableOrgs)
            {
                response.ValidationState = CloudInfrastructureValidationState.ConnectWiseUnassociated;
                return response;
            }

            response.Company = company;
            response.IsPartner = company != null ? await _connectWiseService.IsPartnerCompany(company) : false;

            var accounts = new List<Apis.CloudStack.Model.User>();

            var accountCount = 0;

            if (provisioningFlow != ProvisioningRegistrationFlow.SubOrg && isUserInCurrentOrganization)
            {
                accounts = await GetAccounts(organizationId, user.Email);

                if (response.IsPartner)
                {
                    accountCount = accounts.Where(a => a.IsPartnerUser).Count();
                }
                else
                {
                    accountCount = accounts.Where(a => !a.IsPartnerUser).Count();
                }
            }

            AcCwAccountMap[] companyMaps = new AcCwAccountMap[] { };
            if (company != null)
            {
                companyMaps = await _dbContext.AcCwAccountMap.AsNoTracking().Where(acctMap => acctMap.CwCompanyId == company.Id).ToArrayAsync();
            }

            switch (accountCount)
            {
                case 0:
                    // Username not attached to any CI Accounts
                    // Ensure that this CW Company is not already attached to a CI Account/Domain
                    if (company != null && companyMaps.Count() > 0)
                    {
                        // This CW Company is already attached to a Cloud Infrastructure Account/Domain, and this user
                        // doesn't have rights there, so we can't assume, and grant them something
                        // In the future, this could queue up a user creation request within Cloud Infrastructure
                        response.ValidationState = CloudInfrastructureValidationState.Unauthorized;
                    }
                    else
                    {
                        // No CW Company associations to Cloud Infrastructure, pass back null Ids
                        response.ValidationState = CloudInfrastructureValidationState.Ok;
                        response.AccountId = null;
                        response.DomainId = null;
                    }

                    break;
                case 1:
                    response.ValidationState = CloudInfrastructureValidationState.Ok;
                    response.Account = accounts.First();
                    response.AccountAlreadyLinked = true;

                    // Set the account to the account their email is on
                    // If this account is a DomainAdmin role on a non-ROOT domain, set the domain to that domain
                    if (response.Account.IsSuborgUser)
                    {
                        // FUTURE!!! Supporting SubOrgs
                        // No AcCwAccountMap needed, as we don't bill suborg users, we bill the partner
                        response.AccountId = response.Account.AccountId;
                    }
                    else
                    {
                        if (response.Account.IsPartnerUser)
                        {
                            response.DomainId = response.Account.DomainId;
                            response.AccountId = response.Account.AccountId;
                        }
                        else
                        {
                            // Non-partner, use the AccountId only
                            response.AccountId = response.Account.AccountId;
                        }

                        // Check any existing CI-CW mapping to ensure that if it exists, it matches this account/domain
                        if (companyMaps.Count() > 0)
                        {
                            var acId = response.DomainId ?? response.AccountId;
                            var accountMap = _dbContext.AcCwAccountMap.FirstOrDefault(acctMap => acctMap.AcId == acId);
                            if (accountMap == null)
                            {
                                // Error state, this CW Company has mappings to CI, but not to this CI Account/Domain that the user is associated with
                                response.ValidationState = CloudInfrastructureValidationState.Mismatch;
                            }
                            else
                            {
                                // Mapping between CI and CW already exists
                                response.ValidationState = CloudInfrastructureValidationState.Ok;
                            }
                        }
                    }

                    break;
                default:
                    // If there is already a mapping between one of the CI Accounts and the CW Account, then there is nothing to be done, don't thrown an error
                    bool existingMap = false;
                    if (companyMaps.Count() > 0)
                    {
                        foreach (var companyMap in companyMaps)
                        {
                            var match = accounts.FirstOrDefault(acct => (companyMap.AcType == "Account" ? acct.AccountId : acct.DomainId) == companyMap.AcId);
                            if (match != null)
                            {
                                existingMap = true;
                                response.Account = match;
                                response.AccountId = match.AccountId;
                                if (companyMap.AcType == "Domain")
                                {
                                    response.DomainId = match.DomainId;
                                }

                                break;
                            }
                        }
                    }

                    // If there is already a mapping for this CI entity, then that's ok, otherwise, we cannot determine proper account, thus it is ambiguous
                    response.ValidationState = existingMap ? CloudInfrastructureValidationState.Ok : CloudInfrastructureValidationState.Ambiguous;
                    break;
            }

            return response;
        }

        public async Task<int> MapCompanyToCloudInfrastructure(int userId, int organizationId, CheckCloudInfrastructureResponse validationResponse)
        {
            // TODO Send us an email (configurable addresses) for errors
            // TODO Do sanity checks at the end and send emails to us with failures

            // By the time we get here, we should have an Ok status AND an AccountId. We may also have a DomainId, but that depends on if it is a partner
            if (validationResponse.ValidationState != CloudInfrastructureValidationState.Ok || validationResponse.AccountId == null)
            {
                return 0;
            }

            Guid accountId = validationResponse.AccountId.Value;
            Guid? domainId = validationResponse.DomainId;

            var cwConfig = await _configurationService.GetConnectWiseConfiguration();
            var agreementType = await _connectWiseApi.GetAgreementType(cwConfig.AgreementTypeName);
            var user = await _personRepository.GetPersonById(userId, true);

            if (validationResponse.Company == null)
            {
                return 0;
            }

            var company = validationResponse.Company;

            var accountMap = _dbContext.AcCwAccountMap.FirstOrDefault(acctMap => acctMap.AcId == accountId || domainId != null && acctMap.AcId == domainId);
            var companyMap = _dbContext.AcCwAccountMap.FirstOrDefault(acctMap => acctMap.CwCompanyId == company.Id);

            // Query the CW Company to see if it already has an agreement with the desired name and type
            var agreement = await GetCloudInfrastructureAgreement(company.Id);
            if (agreement != null)
            {
                var agreementMaps = _dbContext.AcCwAccountMap.Where(acctMap => acctMap.CwAgreementId == agreement.Id);
                if (agreementMaps.Count() > 0
                    && agreementMaps.Any(map =>
                        map.CwCompanyId != agreement.Company.Id
                        || map.AcType == "Account" && map.AcId != accountId
                        || map.AcType == "Domain" && map.AcId != domainId
                    ))
                {
                    // TODO Error state, we have an existing AcCwAccountMap that is mapped to the same agreement, but a different Company in CW or entity in CI
                    return 0;
                }
            }
            else
            {
                var contact = (await _connectWiseApi.GetContactsByEmail(user.Email, company.Id)).FirstOrDefault();
                if (contact == null)
                {
                    return 0;
                }

                // No agreement, create one
                // Go to the next first of the month, at least one month out
                var now = DateTime.Now.AddMonths(1);
                var billStartDate = now.Day == 1 ? now : now.AddMonths(1).AddDays(-1 * (now.Day - 1));
                var startDate = DateTime.IsLeapYear(DateTime.Now.Year) && DateTime.Now.Month == 2 && DateTime.Now.Day == 29
                    ? DateTime.Now.AddDays(-1).ToString("yyyy-MM-ddThh:mm:ssZ")
                    : DateTime.Now.ToString("yyyy-MM-ddThh:mm:ssZ");
                var newAgreement = new CreateAgreement
                {
                    Name = cwConfig.AgreementName,
                    StartDate = startDate,
                    // TODO Once we have the contract in the UI, we can change this to something else, like Now, or whatever we decide
                    BillStartDate = billStartDate.ToString("yyyy-MM-ddThh:mm:ssZ"),
                    Type = agreementType,
                    Company = company,
                    Contact = new ContactRef { Id = contact.Id },
                    BillingCycle = await _connectWiseApi.GetBillingCycleType(cwConfig.BillingCycleName),
                    NoEndingDateFlag = true,
                };
                agreement = await _connectWiseApi.CreateAgreement(newAgreement);
            }

            // If no existing mapping, create one
            if (accountMap == null)
            {
                accountMap = new AcCwAccountMap()
                {
                    AcType = domainId == null ? "Account" : "Domain",
                    AcId = domainId == null ? accountId : domainId.Value,
                    AcName = domainId == null ? validationResponse.Account.Account : validationResponse.Account.Domain,
                    CwCompanyId = company.Id,
                    CwCompanyIdentifier = company.Identifier,
                    CwCompanyName = company.Name,
                    CwAgreementId = agreement.Id,
                    CwAgreementName = agreement.Name,
                    Enabled = true,
                    CreatedBy = userId,
                };
                _dbContext.AcCwAccountMap.Add(accountMap);
            }
            else
            {
                // TODO Update the account map
            }

            await _dbContext.SaveChangesAsync();

            return accountMap.Id;
        }

        public async Task<List<Apis.CloudStack.Model.User>> GetAccounts(int organizationId, string userEmail)
        {
            var accounts = await _cloudStackApi.GetUsersByUsername(userEmail);
            var accountIds = accounts.Select(account => account.AccountId);
            var existingAccountIds = await _organizationMappingRepository.GetCloudInfraExistingMappingIds(organizationId, accountIds);
            return accounts.Where(account => !existingAccountIds.Contains(account.AccountId)).ToList();
        }

        private async Task<UserRole> CreateUserRole(int userId, int organizationId, DateTime createdDate, int role, bool isApproved)
        {
            // Ensure that the organization is active
            var inactiveOrg = await _dbContext.Organization
                .FirstOrDefaultAsync(org => org.OrganizationId == organizationId && !org.IsActive);
            if (inactiveOrg != null)
            {
                inactiveOrg.IsActive = true;
            }

            var userRole = await _userRoleRepository.GetByKeys(userId, organizationId, role);

            if (userRole == null)
            {
                _logger.LogInformation($"User organization role does not exist. Creating for User Id {userId} and Org Id {organizationId} with Role {role}");

                var user = await _userService.GetByPersonAndOrganization(userId, organizationId);
                if (user == null)
                {
                    _logger.LogInformation($"User Organization does not exist. Creating for User Id {userId} and Org Id {organizationId}");
                    user = await _userRepository.CreateUser(userId, organizationId);
                }

                userRole = await _userRoleRepository.Create(new UserRole
                {
                    RoleId = role,
                    IsApproved = isApproved,
                    CreatedBy = userId,
                    CreatedDate = createdDate,
                    UserId = user.Id
                });
            }
            else
            {
                _logger.LogInformation($"User Organization Role already exists. Updating for User Id {userId} and Org Id {organizationId}");
                userRole.RoleId = role;
                userRole.IsApproved = userRole.IsApproved || isApproved; // Don't allow an existing approved flag to get overwritten by not approved
                userRole.UpdatedBy = userId;
                userRole.UpdatedDate = createdDate;
                await _dbContext.SaveChangesAsync();
            }

            return userRole;
        }

        private async Task CreateContact(Person user, CreateCompanyAndContactRequest request, string identifier)
        {
            var contact = new Contact
            {
                FirstName = user.FirstName,
                LastName = user.LastName,
                /// If we are creating a new company (right now that is always the case if we got here) then make this contact the primary
                DefaultFlag = true,
                Company = new CompanyRef
                {
                    Identifier = identifier,
                    Name = request.Name
                },
                CommunicationItems = new List<CommunicationItemRef>
                {
                    new CommunicationItemRef
                    {
                        Value = user.Email,
                        Type = new CommunicationTypeRef() { Name = "Email" }
                    }
                }
            };
            _logger.LogInformation($"Registration Process: CreateContact connectWiseApi for User: {user.FirstName} {user.LastName} and Identifier: {identifier}");

            await _connectWiseApi.CreateContact(contact);
        }

        private async Task CreateCompany(CreateCompanyAndContactRequest request, string identifier)
        {
            var cwConfig = await _configurationService.GetConnectWiseConfiguration();
            var companyTypeRef = await _connectWiseApi.GetCompanyType(request.IsPartner ? cwConfig.PartnerTypeName : cwConfig.CustomerTypeName);

            var company = new Company
            {
                Name = request.Name,
                Identifier = identifier,
                AddressLine1 = request.AddressLine1,
                City = request.City,
                State = request.State,
                Zip = request.ZipCode,
                Site = new CompanySite
                {
                    Name = "Main"
                },
                Types = new List<CompanyTypeRef>
                {
                    new CompanyTypeRef
                    {
                        Id = companyTypeRef.Id,
                        Name = companyTypeRef.Name
                    }
                }
            };
            _logger.LogInformation($"Registration Process: CreateCompany connectWiseApi for Company: {request.Name} and Identifier: {identifier}");

            await _connectWiseApi.CreateCompany(company);
        }

        private async Task<int> MapCompanyToNewOrganization(int userId, Company company, DateTime createdDate)
        {
            var isPartner = await _connectWiseService.IsPartnerCompany(company);
            var organization = new OrganizationData.Organization
            {
                AllowSubOrg = isPartner,
                AllowWhiteLabel = true,
                Name = company.Name,
                IsVerified = false,
                IsPartner = isPartner,
                ParentOrganizationId = Constants.RootOrganizationId,
                CreatedBy = userId,
                CreatedDate = createdDate
            };

            organization = await this._organizationRepository.CreateOrganization(organization);

            var organizationMapping = new ConnectWiseOrganizationMapping
            {
                OrganizationId = organization.OrganizationId,
                CompanyId = company.Id,
                CreatedBy = userId,
                CompanyName = company.Name,
            };

            await this._organizationRepository.CreateOrganizationMapping(organizationMapping);

            return organization.OrganizationId;
        }

        public async Task<(string domain, string adaptiveCloudDomain)> GetDomainInfoFromHostName(string hostName)
        {
            var hostParts = hostName.Split('.');
            if (hostParts.Length < 3)
            {
                throw new BadRequestException("Hostname must have at least three parts, eg. aaa.bbb.ccc");
            }

            var allowedDomains = (await _configurationService.GetCloudInfrastructureConfiguration()).AllowedDomains;

            // Use second level domain as domain name by default, which is always the second from the right
            string domainName = hostParts[^2];
            var adaptiveCloudDomain = allowedDomains.FirstOrDefault(domain => domain == $"{hostParts[^2]}.{hostParts[^1]}");
            if (adaptiveCloudDomain != null)
            {
                // Use subdomain as domain name, which is the third from the right
                domainName = hostParts[^3];
            }

            return (domainName, adaptiveCloudDomain);
        }

        private async Task<Agreement> GetCloudInfrastructureAgreement(int companyId)
        {
            var cwConfig = await _configurationService.GetConnectWiseConfiguration();
            var agreementType = await _connectWiseApi.GetAgreementType(cwConfig.AgreementTypeName);

            // Query the CW Company to see if it already has an agreement with the desired name and type
            var agreement = await _connectWiseApi.GetAgreementByNameAndType(companyId, cwConfig.AgreementName, cwConfig.AgreementTypeName);
            if (agreement == null)
            {
                // Load all agreements, and filter to see if there is one that is appropriate for Cloud Infrastructure use
                var agreements = await _connectWiseApi.GetAgreements(companyId);
                DateTime mostRecent = DateTime.UnixEpoch;
                foreach (var agr in agreements)
                {
                    // Filter on the agreements to see if there is one that meets our criteria
                    // Check for configured products and if multiple agreements found, use the one with the latest effective date
                    var additions = await _connectWiseApi.GetAgreementProducts(agr.Id, cwConfig.CloudInfrastructureValidationProducts);
                    var newest = additions.Select(addition => addition.EffectiveDate).Max();
                    // If this Agreement has products with newer effective dates, then use it
                    if (newest != null && newest > mostRecent)
                    {
                        agreement = agr;
                        mostRecent = newest.Value;
                    }
                }
            }

            return agreement;
        }

        private Person GetTemporaryUser()
        {
            var user = new Person();
            string userName = "temp-myac-" + Path.GetRandomFileName().Replace(".", "").Substring(0, 8);
            user.Email = userName + "@adaptivecloud.com";
            user.FirstName = "Test";
            user.LastName = "Test_Myac";
            return user;
        }

        public async Task<int> RegisterUserWithInvitationCode(string code, int personId)
        {
            var invitation = await _invitationCodeRepository.GetInvitationByCode(code, false)
                .Include(x => x.Organization)
                .Include(invitation => invitation.Roles)
                .ThenInclude(role => role.Role)
                .FirstOrDefaultAsync();

            await CreateUserWithInvitation(invitation, personId);

            await _invitationCodeRepository.UpdateUsedCountInvitationCode(invitation, personId);

            return personId;
        }

        private async Task CreateUserWithInvitation(InvitationCode invitation, int personId)
        {
            var user = await _userService.GetByPersonAndOrganization(personId, invitation.OrganizationId);

            user ??= await _userRepository.CreateUser(personId, invitation.OrganizationId);

            var createdDate = DateTime.UtcNow;

            foreach (var item in invitation.Roles)
            {
                await _userRoleRepository.Create(new UserRole
                {
                    RoleId = item.RoleId,
                    IsApproved = false,
                    CreatedBy = invitation.CreatedBy,
                    CreatedDate = createdDate,
                    UserId = user.Id
                });
            }

            await SendEmailForInvitationCode(personId, invitation);
        }

        private async Task SendEmailForInvitationCode(int personId, InvitationCode invitationCode)
        {
            var person = await _personRepository.GetPersonById(personId);
            var emailMessage = await BuildMessage(person, invitationCode);
            var smtpConfiguration = await _configurationService.GetEmailSenderWorkerConfiguration();

            await _emailSenderService.EnqueueEmailMessage(person.Email, emailMessage, "MyAdaptiveCloud: Updated access to " + invitationCode.Organization.Name,
                smtpConfiguration.Email);
        }

        private async Task<string> BuildMessage(Person person, InvitationCode invitation)
        {
            var domainName = (await _whiteLabelService.GetWhiteLabel(invitation.OrganizationId, null))?.DomainName;
            if (string.IsNullOrEmpty(domainName))
            {
                domainName = (await _whiteLabelService.GetDefaultWhitelabel())?.DomainName;
            }

            _logger.LogInformation($"User Role Creation. Domain resolved to {domainName}");

            var message = new StringBuilder($"<p> " + person.FirstName + " " + person.LastName + " has updated your access for " + invitation.Organization + ":</p>");
            message.Append($"<p>Roles Added:</p>");

            var rolesli = string.Empty;
            foreach (var item in invitation.Roles)
            {
                rolesli += $"<li>{item.Role.Name}</li>";
            }

            message.Append($"<p><ul>{rolesli}</ul></p>");
            message.Append("<p>You may use the link below to login:</p>");
            if (!string.IsNullOrEmpty(domainName))
            {
                message.Append($"\r\n<p><a href=\"" + domainName + "\">" + domainName + "</a></p>");
            }

            return message.ToString();
        }
    }
}