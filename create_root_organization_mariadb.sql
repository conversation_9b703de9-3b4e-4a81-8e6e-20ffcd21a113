-- liquibase formatted sql

-- changeset example:create-root-organization-mariadb context:main endDelimiter:|
-- Create Root organization with proper MariaDB syntax
BEGIN NOT ATOMIC
    DECLARE org_count INT DEFAULT 0;
    
    -- Check if Root organization with ID 0 already exists
    SELECT COUNT(*) INTO org_count 
    FROM Organization 
    WHERE OrganizationId = 0 AND Name = 'Root';
    
    -- Only create if it doesn't exist
    IF org_count = 0 THEN
        -- Insert the organization first
        INSERT INTO Organization (Name, CreatedDate, IsActive, AllowSubOrg, AllowWhiteLabel, Is<PERSON>artner, IsVerified, ParentOrganizationId) 
        VALUES ('Root', NOW(), 1, 1, 1, 0, 1, NULL);
        
        -- Update the newly inserted organization to have ID 0
        UPDATE Organization 
        SET OrganizationId = 0 
        WHERE OrganizationId = LAST_INSERT_ID() AND Name = 'Root';
    END IF;
END|
